import requests
import base64
import json

def file2base64(file_path):
    with open(file_path, 'rb') as f:
        base64_data = base64.b64encode(f.read()).decode()

    return base64_data

api = 'http://127.0.0.1:8083/OcrWeb/azApi/costSettlementSum_ocrd'
# api = 'http://192.168.1.137:18089/OcrWeb/azApi/costSettlementSum_ocrd'
fpath = r"C:\Users\<USER>\Desktop\财务域能力开发-cbl\成本结算表\chengben\jiesuan_0178.png"
fpath  = r"C:\Users\<USER>\Downloads\image_to_pdf_docsmall.com (1).pdf"
data = {
    'base64_strs': file2base64(fpath)
}
data = json.dumps(data)
hdr = {'Content-Type': 'application/json'}
res = requests.post(api, data=data, headers=hdr)
print(res.text)