"""
这是修改后的 check_post_data 方法，增加了 PDF/DOC 转图片功能
请确认后再应用到主文件
"""

from document_converter import DocumentConverter
import copy
import base64
import numpy as np
import cv2
from flask import Response
import json
import logging
from tools import is_valid_base64, get_base64_size, get_base64_type

# 假设这些是类的属性
logger = logging.getLogger(__name__)

def check_post_data_updated(self, post_map):
    """
    更新后的 check_post_data 方法
    增加了 PDF/DOC 转图片功能
    """
    api_res = copy.deepcopy(self.base_api_res)
    
    try:
        file_base64 = post_map.get("imgBase64", "")
    except Exception as e:
        logger.warning("缺失参数：", e)
        api_res['code'] = 30001
        api_res['msg'] = '缺少参数'
        return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
    
    if not is_valid_base64(file_base64):
        logger.warning("参数无效")
        api_res['code'] = 30002
        api_res['msg'] = '文件无效，请输入有效的base64编码'
        return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
    
    fsize = get_base64_size(file_base64)
    if fsize < 1024 * 50 or fsize > 1024 * 1024 * 30:
        logger.warning("图片太小，请上传大于50kB，小于30MB的图片")
        api_res['code'] = 30003
        api_res['msg'] = '图片太小，请上传大于50kB，小于30MB的图片'
        return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
    
    ftype = get_base64_type(file_base64)
    if ftype not in ["doc", "docx", "pdf", "img"]:
        logger.warning("不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、doc文件")
        api_res['code'] = 30004
        api_res['msg'] = '不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、doc文件'
        return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
    
    try:
        # 根据文件类型处理
        if ftype == "img":
            # 原有的图片处理逻辑
            image_data = base64.b64decode(file_base64)
            image_array = np.frombuffer(image_data, dtype=np.uint8)
            cv_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if cv_image is None:
                logger.warning("图片解码失败")
                api_res['code'] = 30005
                api_res['msg'] = '图片解码失败，请检查图片格式'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
                
        elif ftype in ["pdf", "doc", "docx"]:
            # 新增的文档转图片功能
            logger.info(f"检测到 {ftype} 文件，开始转换为图片...")
            
            # 初始化文档转换器（如果还没有的话）
            if not hasattr(self, 'doc_converter'):
                self.doc_converter = DocumentConverter()
            
            try:
                # 转换文档为图片
                cv_image = self.doc_converter.convert_document_to_image(file_base64, ftype)
                logger.info(f"{ftype} 文件转换成功")
                
            except Exception as convert_error:
                logger.error(f"{ftype} 转图片失败: {str(convert_error)}")
                api_res['code'] = 30006
                api_res['msg'] = f'{ftype.upper()} 文件转换失败: {str(convert_error)}'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        else:
            # 理论上不会到达这里，但为了安全起见
            logger.warning(f"未知文件类型: {ftype}")
            api_res['code'] = 30007
            api_res['msg'] = f'未知文件类型: {ftype}'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        # 验证最终图片
        if cv_image is None or cv_image.size == 0:
            logger.warning("处理后的图片为空")
            api_res['code'] = 30008
            api_res['msg'] = '处理后的图片为空，请检查文件内容'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        # 检查图片尺寸是否合理
        height, width = cv_image.shape[:2]
        if height < 100 or width < 100:
            logger.warning(f"图片尺寸过小: {width}x{height}")
            api_res['code'] = 30009
            api_res['msg'] = f'图片尺寸过小: {width}x{height}，请提供更大的图片'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        logger.info(f"文件处理成功，最终图片尺寸: {width}x{height}")
        return True, cv_image
        
    except Exception as e:
        logger.error(f"文件处理过程中发生错误: {str(e)}")
        api_res['code'] = 30010
        api_res['msg'] = f'文件处理失败: {str(e)}'
        return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')


# 需要在 BaoZhengJin 类的 __init__ 方法中添加的初始化代码
def __init___addition(self):
    """
    在 BaoZhengJin 类的 __init__ 方法中需要添加的代码
    """
    # 原有的初始化代码...
    
    # 新增：初始化文档转换器
    try:
        self.doc_converter = DocumentConverter()
        logger.info("文档转换器初始化成功")
    except Exception as e:
        logger.warning(f"文档转换器初始化失败: {str(e)}")
        self.doc_converter = None


# 使用示例和测试代码
if __name__ == "__main__":
    # 测试文档转换功能
    converter = DocumentConverter()
    
    # 测试 PDF 文件（需要提供实际的 PDF base64 数据）
    # pdf_base64 = "..."  # 实际的 PDF base64 数据
    # try:
    #     img = converter.convert_document_to_image(pdf_base64, "pdf")
    #     print(f"PDF 转换成功，图片尺寸: {img.shape}")
    # except Exception as e:
    #     print(f"PDF 转换失败: {e}")
    
    print("文档转换器测试完成")
