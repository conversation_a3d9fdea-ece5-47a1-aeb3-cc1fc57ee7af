import requests
import base64
import json

def file2base64(file_path):
    with open(file_path, 'rb') as f:
        base64_data = base64.b64encode(f.read()).decode()

    return base64_data

api = 'http://127.0.0.1:8083/OcrWeb/azApi/businessAgentRecd'
# api = 'http://192.168.1.137:28089/OcrWeb/azApi/businessAgentRecd'
fpath = r"D:\myproj\2025\caiwuyu\acc_img\dailishang\9163.jpg"
fpath = r"C:\Users\<USER>\Desktop\财务域能力开发-cbl\集团代理商退出申请表\新建 DOCX 文档.docx"
data = {
    'imgBase64': file2base64(fpath)
}
data = json.dumps(data)
hdr = {'Content-Type': 'application/json'}
res = requests.post(api, data=data, headers=hdr)
print(res.text)