#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX转换测试脚本
用于调试docx文件转换为图片时出现全白图片的问题
"""

import base64
import cv2
import numpy as np
import os
import sys
from document_converter import DocumentConverter
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_docx_conversion(docx_file_path):
    """
    测试docx文件转换
    
    Args:
        docx_file_path: docx文件路径
    """
    if not os.path.exists(docx_file_path):
        logger.error(f"文件不存在: {docx_file_path}")
        return
    
    # 读取文件并转换为base64
    with open(docx_file_path, 'rb') as f:
        file_data = f.read()
        base64_data = base64.b64encode(file_data).decode('utf-8')
    
    logger.info(f"文件大小: {len(file_data)} 字节")
    logger.info(f"Base64长度: {len(base64_data)}")
    
    # 创建转换器
    converter = DocumentConverter()
    
    # 调试文档内容
    logger.info("=== 开始调试文档内容 ===")
    debug_info = converter.debug_docx_content(base64_data)
    
    print("\n文档调试信息:")
    print(f"段落数量: {debug_info.get('paragraphs_count', 0)}")
    print(f"表格数量: {debug_info.get('tables_count', 0)}")
    print(f"总文本长度: {debug_info.get('total_text_length', 0)}")
    print(f"是否有内容: {debug_info.get('has_content', False)}")
    
    if 'error' in debug_info:
        print(f"错误: {debug_info['error']}")
        return
    
    # 显示段落内容
    print("\n段落内容:")
    for para in debug_info.get('paragraphs_content', []):
        print(f"  段落 {para['index']}: 长度={para['length']}, 空={para['is_empty']}")
        if para['text']:
            print(f"    内容: {para['text'][:100]}...")
    
    # 显示表格内容
    print("\n表格内容:")
    for table in debug_info.get('tables_content', []):
        print(f"  表格 {table['index']}: 有文本的单元格={table['cells_with_text']}")
        if table['sample_text']:
            print(f"    示例: {table['sample_text']}")
    
    # 尝试转换
    logger.info("=== 开始转换为图片 ===")
    try:
        cv_image = converter.convert_document_to_image(base64_data, "docx")
        
        if cv_image is not None:
            height, width = cv_image.shape[:2]
            logger.info(f"转换成功! 图片尺寸: {width}x{height}")
            
            # 保存图片
            output_path = "converted_docx.jpg"
            cv2.imwrite(output_path, cv_image)
            logger.info(f"图片已保存到: {output_path}")
            
            # 检查图片是否为全白
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            mean_value = np.mean(gray)
            std_value = np.std(gray)
            
            print(f"\n图片统计信息:")
            print(f"平均像素值: {mean_value:.2f} (255为全白)")
            print(f"标准差: {std_value:.2f} (0表示完全一致)")
            
            if mean_value > 250 and std_value < 5:
                print("⚠️  警告: 图片可能是全白的!")
                print("可能原因:")
                print("1. 文档内容为空或无法读取")
                print("2. 字体加载失败")
                print("3. 文本颜色为白色")
                print("4. 文档格式不支持")
            else:
                print("✅ 图片看起来正常")
                
        else:
            logger.error("转换失败: 返回的图片为None")
            
    except Exception as e:
        logger.error(f"转换过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def test_simple_docx():
    """创建一个简单的测试docx文件"""
    try:
        from docx import Document
        
        # 创建一个简单的文档
        doc = Document()
        doc.add_heading('测试文档', 0)
        doc.add_paragraph('这是一个测试段落。')
        doc.add_paragraph('This is a test paragraph in English.')
        doc.add_paragraph('包含中文和English的混合文本。')
        
        # 添加表格
        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = '姓名'
        table.cell(0, 1).text = '年龄'
        table.cell(1, 0).text = '张三'
        table.cell(1, 1).text = '25'
        
        # 保存测试文档
        test_file = 'test_document.docx'
        doc.save(test_file)
        logger.info(f"测试文档已创建: {test_file}")
        
        # 测试转换
        test_docx_conversion(test_file)
        
    except ImportError:
        logger.error("无法创建测试文档，请安装 python-docx: pip install python-docx")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 测试指定的docx文件
        docx_file = sys.argv[1]
        test_docx_conversion(docx_file)
    else:
        # 创建并测试简单文档
        print("未指定docx文件，将创建测试文档...")
        test_simple_docx()
        
        print("\n使用方法:")
        print(f"python {sys.argv[0]} <docx文件路径>")
