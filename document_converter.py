"""
文档转图片工具类
支持 PDF 和 DOC/DOCX 文件的第一页转换为图片
"""
import base64
import io
import tempfile
import os
import cv2
import numpy as np
import logging

# 设置日志
logger = logging.getLogger(__name__)

class DocumentConverter:
    """文档转图片转换器"""
    
    def __init__(self):
        """初始化转换器，检查依赖库"""
        self.pdf_available = False
        self.doc_available = False
        
        # 检查 PDF 处理库
        try:
            import fitz  # PyMuPDF
            self.pdf_available = True
            self.pdf_lib = 'fitz'
            logger.info("使用 PyMuPDF 处理 PDF 文件")
        except ImportError:
            try:
                from pdf2image import convert_from_bytes
                self.pdf_available = True
                self.pdf_lib = 'pdf2image'
                logger.info("使用 pdf2image 处理 PDF 文件")
            except ImportError:
                logger.warning("未找到 PDF 处理库，请安装 PyMuPDF 或 pdf2image")
        
        # 检查 DOC 处理库
        try:
            from docx import Document
            from PIL import Image, ImageDraw, ImageFont
            self.doc_available = True
            logger.info("DOC/DOCX 处理库可用")
        except ImportError:
            logger.warning("未找到 DOC 处理库，请安装 python-docx 和 Pillow")
    
    def pdf_to_image(self, pdf_data: bytes) -> np.ndarray:
        """
        将 PDF 第一页转换为图片
        
        Args:
            pdf_data: PDF 文件的字节数据
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        if not self.pdf_available:
            raise Exception("PDF 处理库不可用，请安装 PyMuPDF 或 pdf2image")
        
        try:
            if self.pdf_lib == 'fitz':
                return self._pdf_to_image_fitz(pdf_data)
            else:
                return self._pdf_to_image_pdf2image(pdf_data)
        except Exception as e:
            logger.error(f"PDF 转图片失败: {str(e)}")
            raise Exception(f"PDF 转图片失败: {str(e)}")
    
    def _pdf_to_image_fitz(self, pdf_data: bytes) -> np.ndarray:
        """使用 PyMuPDF 转换 PDF"""
        import fitz
        
        # 从字节数据创建 PDF 文档
        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
        
        if pdf_document.page_count == 0:
            raise Exception("PDF 文件为空")
        
        # 获取第一页
        page = pdf_document[0]
        
        # 设置渲染参数（提高分辨率）
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        # 转换为 PIL Image
        img_data = pix.tobytes("ppm")
        
        # 转换为 numpy 数组
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        pdf_document.close()
        
        if img is None:
            raise Exception("PDF 页面渲染失败")
        
        return img
    
    def _pdf_to_image_pdf2image(self, pdf_data: bytes) -> np.ndarray:
        """使用 pdf2image 转换 PDF"""
        from pdf2image import convert_from_bytes
        from PIL import Image
        
        # 转换第一页
        images = convert_from_bytes(pdf_data, first_page=1, last_page=1, dpi=200)
        
        if not images:
            raise Exception("PDF 转换失败，未生成图片")
        
        # 获取第一页
        pil_image = images[0]
        
        # 转换为 OpenCV 格式
        img_array = np.array(pil_image)
        
        # PIL 使用 RGB，OpenCV 使用 BGR
        if len(img_array.shape) == 3:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_array
    
    def doc_to_image(self, doc_data: bytes, file_type: str = "doc") -> np.ndarray:
        """
        将 DOC/DOCX 第一页转换为图片
        
        Args:
            doc_data: DOC/DOCX 文件的字节数据
            file_type: 文件类型 ("doc" 或 "docx")
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        if not self.doc_available:
            raise Exception("DOC 处理库不可用，请安装 python-docx 和 Pillow")
        
        try:
            return self._doc_to_image_docx(doc_data)
        except Exception as e:
            logger.error(f"DOC 转图片失败: {str(e)}")
            raise Exception(f"DOC 转图片失败: {str(e)}")
    
    def _doc_to_image_docx(self, doc_data: bytes) -> np.ndarray:
        """使用 python-docx 转换 DOC/DOCX"""
        from docx import Document
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(doc_data)
            temp_file_path = temp_file.name
        
        try:
            # 读取文档
            doc = Document(temp_file_path)
            
            if not doc.paragraphs:
                raise Exception("文档为空或无法读取内容")
            
            # 创建图片画布
            img_width, img_height = 800, 1000  # A4 比例
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                try:
                    font = ImageFont.truetype("simhei.ttf", 16)  # 中文字体
                except:
                    font = ImageFont.load_default()
            
            # 渲染文本
            y_position = 50
            line_height = 25
            margin = 50
            
            for paragraph in doc.paragraphs[:20]:  # 只处理前20段
                if y_position > img_height - 100:  # 防止超出页面
                    break
                
                text = paragraph.text.strip()
                if text:
                    # 处理长文本换行
                    words = text.split()
                    lines = []
                    current_line = ""
                    
                    for word in words:
                        test_line = current_line + " " + word if current_line else word
                        bbox = draw.textbbox((0, 0), test_line, font=font)
                        if bbox[2] - bbox[0] <= img_width - 2 * margin:
                            current_line = test_line
                        else:
                            if current_line:
                                lines.append(current_line)
                            current_line = word
                    
                    if current_line:
                        lines.append(current_line)
                    
                    # 绘制文本行
                    for line in lines:
                        if y_position > img_height - 100:
                            break
                        draw.text((margin, y_position), line, fill='black', font=font)
                        y_position += line_height
                    
                    y_position += 10  # 段落间距
            
            # 转换为 OpenCV 格式
            img_array = np.array(img)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            return img_cv
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
    
    def convert_document_to_image(self, base64_data: str, file_type: str) -> np.ndarray:
        """
        统一的文档转图片接口
        
        Args:
            base64_data: base64 编码的文件数据
            file_type: 文件类型 ("pdf", "doc", "docx")
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        try:
            # 解码 base64 数据
            file_data = base64.b64decode(base64_data)
            
            if file_type.lower() == "pdf":
                return self.pdf_to_image(file_data)
            elif file_type.lower() in ["doc", "docx"]:
                return self.doc_to_image(file_data, file_type)
            else:
                raise Exception(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"文档转换失败: {str(e)}")
            raise Exception(f"文档转换失败: {str(e)}")
