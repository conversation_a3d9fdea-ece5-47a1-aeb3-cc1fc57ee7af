"""
文档转图片工具类
支持 PDF 和 DOC/DOCX 文件的第一页转换为图片
"""
import base64
import io
import tempfile
import os
import cv2
import numpy as np
import logging

# 设置日志
logger = logging.getLogger(__name__)

class DocumentConverter:
    """文档转图片转换器"""
    
    def __init__(self):
        """初始化转换器，检查依赖库"""
        self.pdf_available = False
        self.doc_available = False
        
        # 检查 PDF 处理库
        try:
            import fitz  # PyMuPDF
            self.pdf_available = True
            self.pdf_lib = 'fitz'
            logger.info("使用 PyMuPDF 处理 PDF 文件")
        except ImportError:
            try:
                from pdf2image import convert_from_bytes
                self.pdf_available = True
                self.pdf_lib = 'pdf2image'
                logger.info("使用 pdf2image 处理 PDF 文件")
            except ImportError:
                logger.warning("未找到 PDF 处理库，请安装 PyMuPDF 或 pdf2image")
        
        # 检查 DOC 处理库
        try:
            from docx import Document
            from PIL import Image, ImageDraw, ImageFont
            self.doc_available = True
            logger.info("DOC/DOCX 处理库可用")
        except ImportError:
            logger.warning("未找到 DOC 处理库，请安装 python-docx 和 Pillow")
    
    def pdf_to_image(self, pdf_data: bytes) -> np.ndarray:
        """
        将 PDF 第一页转换为图片
        
        Args:
            pdf_data: PDF 文件的字节数据
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        if not self.pdf_available:
            raise Exception("PDF 处理库不可用，请安装 PyMuPDF 或 pdf2image")
        
        try:
            if self.pdf_lib == 'fitz':
                return self._pdf_to_image_fitz(pdf_data)
            else:
                return self._pdf_to_image_pdf2image(pdf_data)
        except Exception as e:
            logger.error(f"PDF 转图片失败: {str(e)}")
            raise Exception(f"PDF 转图片失败: {str(e)}")
    
    def _pdf_to_image_fitz(self, pdf_data: bytes) -> np.ndarray:
        """使用 PyMuPDF 转换 PDF"""
        import fitz
        
        # 从字节数据创建 PDF 文档
        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
        
        if pdf_document.page_count == 0:
            raise Exception("PDF 文件为空")
        
        # 获取第一页
        page = pdf_document[0]
        
        # 设置渲染参数（提高分辨率）
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        # 转换为 PIL Image
        img_data = pix.tobytes("ppm")
        
        # 转换为 numpy 数组
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        pdf_document.close()
        
        if img is None:
            raise Exception("PDF 页面渲染失败")
        
        return img
    
    def _pdf_to_image_pdf2image(self, pdf_data: bytes) -> np.ndarray:
        """使用 pdf2image 转换 PDF"""
        from pdf2image import convert_from_bytes
        from PIL import Image
        
        # 转换第一页
        images = convert_from_bytes(pdf_data, first_page=1, last_page=1, dpi=200)
        
        if not images:
            raise Exception("PDF 转换失败，未生成图片")
        
        # 获取第一页
        pil_image = images[0]
        
        # 转换为 OpenCV 格式
        img_array = np.array(pil_image)
        
        # PIL 使用 RGB，OpenCV 使用 BGR
        if len(img_array.shape) == 3:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_array
    
    def doc_to_image(self, doc_data: bytes, file_type: str = "doc") -> np.ndarray:
        """
        将 DOC/DOCX 第一页转换为图片
        
        Args:
            doc_data: DOC/DOCX 文件的字节数据
            file_type: 文件类型 ("doc" 或 "docx")
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        if not self.doc_available:
            raise Exception("DOC 处理库不可用，请安装 python-docx 和 Pillow")
        
        try:
            return self._doc_to_image_docx(doc_data)
        except Exception as e:
            logger.error(f"DOC 转图片失败: {str(e)}")
            raise Exception(f"DOC 转图片失败: {str(e)}")
    
    def _doc_to_image_docx(self, doc_data: bytes) -> np.ndarray:
        """使用 python-docx 转换 DOC/DOCX"""
        from docx import Document
        from PIL import Image, ImageDraw, ImageFont
        import io

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(doc_data)
            temp_file_path = temp_file.name

        try:
            # 读取文档
            doc = Document(temp_file_path)

            # 创建图片画布
            img_width, img_height = 800, 1000  # A4 比例
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)

            # 尝试加载字体（改进字体加载逻辑）
            font = self._load_font()

            # 收集所有文本内容
            all_text = self._extract_all_text(doc)

            if not all_text.strip():
                logger.warning("文档中没有找到可显示的文本内容")
                # 绘制一个提示信息，避免完全空白
                draw.text((50, 50), "文档内容为空或无法读取", fill='red', font=font)
                draw.text((50, 80), f"文档包含 {len(doc.paragraphs)} 个段落", fill='red', font=font)
                draw.text((50, 110), f"文档包含 {len(doc.tables)} 个表格", fill='red', font=font)
            else:
                # 渲染文本
                self._render_text_to_image(draw, all_text, font, img_width, img_height)

            # 转换为 OpenCV 格式
            img_array = np.array(img)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 保存调试图片
            debug_path = "debug_docx_conversion.jpg"
            cv2.imwrite(debug_path, img_cv)
            logger.info(f"调试图片已保存到: {debug_path}")

            return img_cv

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    def _load_font(self):
        """改进的字体加载逻辑"""
        from PIL import ImageFont

        # 字体候选列表（按优先级排序）
        font_candidates = [
            # Windows 系统字体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/arial.ttf",   # Arial
            "C:/Windows/Fonts/calibri.ttf", # Calibri
            # Linux 系统字体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            # macOS 系统字体
            "/System/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/Helvetica.ttc",
        ]

        for font_path in font_candidates:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, 16)
                    logger.info(f"成功加载字体: {font_path}")
                    return font
            except Exception as e:
                logger.debug(f"无法加载字体 {font_path}: {e}")
                continue

        # 如果所有字体都加载失败，使用默认字体
        logger.warning("所有字体加载失败，使用默认字体")
        return ImageFont.load_default()

    def _extract_all_text(self, doc):
        """提取文档中的所有文本内容"""
        all_text = []

        # 提取段落文本
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                all_text.append(text)

        # 提取表格文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text = cell.text.strip()
                    if text:
                        all_text.append(text)

        return "\n".join(all_text)

    def _render_text_to_image(self, draw, text, font, img_width, img_height):
        """将文本渲染到图片上"""
        y_position = 50
        line_height = 25
        margin = 50
        max_width = img_width - 2 * margin

        # 按行分割文本
        lines = text.split('\n')

        for line in lines[:30]:  # 限制行数避免超出页面
            if y_position > img_height - 100:
                break

            if not line.strip():
                y_position += line_height // 2  # 空行
                continue

            # 处理长行的换行
            wrapped_lines = self._wrap_text(draw, line, font, max_width)

            for wrapped_line in wrapped_lines:
                if y_position > img_height - 100:
                    break
                draw.text((margin, y_position), wrapped_line, fill='black', font=font)
                y_position += line_height

            y_position += 5  # 段落间距

    def _wrap_text(self, draw, text, font, max_width):
        """文本换行处理"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            try:
                bbox = draw.textbbox((0, 0), test_line, font=font)
                text_width = bbox[2] - bbox[0]
            except:
                # 如果textbbox不可用，使用textsize（旧版本PIL）
                try:
                    text_width = draw.textsize(test_line, font=font)[0]
                except:
                    # 如果都不可用，估算宽度
                    text_width = len(test_line) * 10

            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines
    
    def convert_document_to_image(self, base64_data: str, file_type: str) -> np.ndarray:
        """
        统一的文档转图片接口
        
        Args:
            base64_data: base64 编码的文件数据
            file_type: 文件类型 ("pdf", "doc", "docx")
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        try:
            # 解码 base64 数据
            file_data = base64.b64decode(base64_data)
            
            if file_type.lower() == "pdf":
                return self.pdf_to_image(file_data)
            elif file_type.lower() in ["doc", "docx"]:
                return self.doc_to_image(file_data, file_type)
            else:
                raise Exception(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"文档转换失败: {str(e)}")
            raise Exception(f"文档转换失败: {str(e)}")

    def debug_docx_content(self, base64_data: str) -> dict:
        """
        调试docx文档内容，返回详细信息

        Args:
            base64_data: base64 编码的docx文件数据

        Returns:
            dict: 包含文档详细信息的字典
        """
        try:
            from docx import Document
            import tempfile

            # 解码 base64 数据
            file_data = base64.b64decode(base64_data)

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name

            try:
                # 读取文档
                doc = Document(temp_file_path)

                debug_info = {
                    "paragraphs_count": len(doc.paragraphs),
                    "tables_count": len(doc.tables),
                    "paragraphs_content": [],
                    "tables_content": [],
                    "total_text_length": 0,
                    "has_content": False
                }

                # 分析段落
                for i, paragraph in enumerate(doc.paragraphs[:10]):  # 只显示前10段
                    text = paragraph.text.strip()
                    debug_info["paragraphs_content"].append({
                        "index": i,
                        "text": text,
                        "length": len(text),
                        "is_empty": not bool(text)
                    })
                    if text:
                        debug_info["has_content"] = True
                        debug_info["total_text_length"] += len(text)

                # 分析表格
                for i, table in enumerate(doc.tables[:5]):  # 只显示前5个表格
                    table_text = []
                    for row in table.rows:
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            if cell_text:
                                table_text.append(cell_text)
                                debug_info["total_text_length"] += len(cell_text)
                                debug_info["has_content"] = True

                    debug_info["tables_content"].append({
                        "index": i,
                        "cells_with_text": len(table_text),
                        "sample_text": table_text[:3] if table_text else []
                    })

                return debug_info

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except Exception as e:
            return {
                "error": str(e),
                "has_content": False
            }
