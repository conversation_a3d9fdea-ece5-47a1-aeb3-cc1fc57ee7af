2025-06-06 10:26:32 - INFO - 使用 PyMuPDF 处理 PDF 文件
2025-06-06 10:26:32 - INFO - DOC/DOCX 处理库可用
2025-06-06 10:26:33 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8083
 * Running on http://*************:8083
2025-06-06 10:26:33 - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 10:27:24 - INFO - 检测到 docx 文件，开始转换为图片...
2025-06-06 10:27:24 - INFO - 成功加载字体: C:/Windows/Fonts/simhei.ttf
2025-06-06 10:27:24 - WARNING - 文档中没有找到可显示的文本内容
2025-06-06 10:27:24 - INFO - 调试图片已保存到: debug_docx_conversion.jpg
2025-06-06 10:27:24 - INFO - docx 文件转换成功
2025-06-06 10:27:24 - INFO - 文件处理成功，最终图片尺寸: 800x1000
2025-06-06 10:27:26 - INFO - {'result': {'isSuc': True, 'code': 0, 'msg': '1.463s', 'res': [{'key': '网点填写', 'value': {'signature': 0, 'date': 0, 'seal': 0, 'handprint': 0, 'refundMoney': '', 'refundReason': '终止办理移动业务', 'refundName': '', 'refundAccount': '', 'bank': '', 'account': ''}}, {'key': '移动公司填写', 'value': {'comment': '同意', 'signature': 0, 'date': 0, 'seal': 0, 'handprint': 0, 'channelName': '', 'channelCode': ''}}, {'key': '网格审核', 'value': {'comment': '同意', 'signature': 0, 'date': 0, 'seal': 0, 'handprint': 0}}, {'key': '部门经理审核', 'value': {'comment': '同意', 'signature': 0, 'date': 0, 'seal': 0, 'handprint': 0}}]}, 'msg': 'success', 'code': 0}
2025-06-06 10:27:26 - INFO - 127.0.0.1 - - [06/Jun/2025 10:27:26] "POST /OcrWeb/azApi/depositRefundRecd HTTP/1.1" 200 -
